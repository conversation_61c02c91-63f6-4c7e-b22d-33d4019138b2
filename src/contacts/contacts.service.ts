import {
    BadRequestException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { Connection, Model } from "mongoose";
import { ContactDocument } from "./schema/contact.schema";
import { CreateContactDto } from "./dto/create-contact.dto";
import { GetContactDto } from "./dto/get-contact.dto";
import { ClientDocument } from "src/client/schema/client.schema";
import { ContactTypeEnum } from "./enum/contact.enum";
import { AddLinkedContactDto } from "./dto/add-linked-contact.dto";
import OkResponse from "src/shared/http/response/ok.http";
import { buildMongoQuery, getTextChange } from "src/shared/helpers/logics";
import { randomUUID } from "crypto";
import { UpdateDndDto } from "./dto/update-dnd.dto";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import { PriceDocument } from "src/project/schema/price-schema";
import { ProjectDocument } from "src/project/schema/project.schema";
import { GetSearchContactDto } from "./dto/search.dto";
import { LeadDocument } from "src/lead/schema/lead.schema";
import { CreateContactCommentDto } from "./dto/create-contact-comment.dto";
import CreatedResponse from "src/shared/http/response/created.http";
import { CreateNewActionDto } from "./dto/create-new-action.dto";
import { UpdateContactActivityDto } from "./dto/update-contact-activity.dto";
import { ActivityLogDocument } from "src/activity-log/schema/activity-log.schema";
import { OpportunityActivityDocument } from "src/opportunity/schema/opportunity-activity-log.schema";
import { LostAndUnLostContactDto } from "./dto/lost-contact.dto";
import { ReferrersDocument } from "src/company/schema/referrers.schema";
import { CrmStageDocument } from "src/crm/schema/crm-stage.schema";
import { StageGroupEnum } from "src/crm/enum/stage-group.enum";
import { PaginationDto } from "src/shared/dto/pagination.dto";

@Injectable()
export class ContactsService {
    constructor(
        @InjectModel("Contact") private contactModel: Model<ContactDocument>,
        @InjectModel("Client") private clientModel: Model<ClientDocument>,
        @InjectModel("Opportunity") private opportunityModel: Model<OpportunityDocument>,
        @InjectModel("Project") private projectModel: Model<ProjectDocument>,
        @InjectModel("Price") private priceModel: Model<PriceDocument>,
        @InjectModel("Lead") private leadModel: Model<LeadDocument>,
        @InjectModel("ActivityLog") private activityLogModel: Model<ActivityLogDocument>,
        @InjectModel("OpportunityActivity")
        private opportunityActivityModel: Model<OpportunityActivityDocument>,
        @InjectModel("Referrers") private readonly referrersModel: Model<ReferrersDocument>,
        @InjectModel("CrmStage") private readonly crmStageModel: Model<CrmStageDocument>,
        @InjectConnection() private readonly connection: Connection,
    ) {
        // this.migrateClientToContact();
        // this.migrateLeadToContact();
        // this.migrateReferrerToContact();
        // this.migrateOpportunityClientIdToContactId();
        // this.migrateOpportunityActivity();
        ////////////
        // this.migrateTrackingAttribution();
        // this.migrateBusinessName();
        // this.checkLeadForContact();
        // this.migrateLeadToContact2();
        // this.migrateLeadStage();
        // this.fixIncorrectContactIdAssignments();
        // this.fixNewLeadDate();
        // this.migrateContactTypesByOpportunity(false); // Set to false to apply changes
        ////////////
        // this.findAll(
        //     {
        //         filter: {
        //             filter: [
        //                 { field: "type", operator: "has_duplicates", value: "businessName" },
        //                 // { field: "type", operator: "is_not", value: "lead" },
        //                 // { field: "type", operator: "is_not", value: "client" },
        //             ],
        //             logic: "AND",
        //         },
        //     },
        //     { companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3" },
        // );
        // this.getLinkedContact("f8486cee-7726-4f82-bb14-bde9a351ad38", {
        //     companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
        // });
        // this.getContactOpportunities("040cc053-5692-471f-869f-7fa2927cfd16", {
        //     companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
        // });
    }

    async create(createContactDto: CreateContactDto, user) {
        let session;
        try {
            session = await this.connection.startSession();
            session.startTransaction();

            let activityBody = `created a New Contact`;
            const contactId = randomUUID();

            if (createContactDto.type === ContactTypeEnum.LEAD) {
                createContactDto.status = "active";
                const stageData = await this.crmStageModel.findOne(
                    {
                        companyId: user.companyId,
                        deleted: false,
                        stageGroup: StageGroupEnum.Leads,
                        sequence: 1,
                    },
                    { _id: 1, defaultCsrId: 1 },
                );
                const stageId = stageData._id;
                const csrId = createContactDto?.csrId || stageData.defaultCsrId;
                if (!createContactDto["newLeadDate"] || createContactDto["newLeadDate"] === undefined)
                    createContactDto["newLeadDate"] = new Date();
                activityBody = `created a New Lead`;
                // createContactDto.newLeadDate = new Date();

                // create lead
                const createdLead = new this.leadModel({
                    contactId,
                    companyId: user.companyId,
                    createdBy: user.memberId,
                    status: "active",
                    stageId,
                    csrId,
                    ...createContactDto,
                });
                await createdLead.save({ session });
            }
            const newContact = new this.contactModel({
                _id: contactId,
                ...createContactDto,
                companyId: user.companyId,
                createdBy: user.memberId,
                dateReceived: new Date(),
            });
            await newContact.save({ session });

            // creating activity
            const createdContactActivity = new this.activityLogModel({
                moduleId: contactId,
                moduleType: "contact",
                companyId: user.companyId,
                activities: [
                    {
                        _id: randomUUID(),
                        body: activityBody,
                        createdBy: user.memberId,
                        createdAt: new Date().toISOString(),
                    },
                ],
            });

            await createdContactActivity.save({ session });
            await session.commitTransaction();

            return new OkResponse({
                id: contactId,
                contact: newContact,
                message: "Contact created successfully",
            });
        } catch (error: any) {
            await session.abortTransaction();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        } finally {
            session.endSession();
        }
    }

    async findAll(getContactDto: GetContactDto, user) {
        try {
            const { limit = 10, skip = 1, search, filter, deleted = false } = getContactDto;
            const skipValue = (skip - 1) * limit;
            const fFilter = filter ? JSON.parse(filter) : null;

            // Ensure the filter includes companyId and deleted status
            const mongoQuery = fFilter ? buildMongoQuery(fFilter) : {};

            // Check if any field has duplicate detection enabled
            const duplicateFields = Object.keys(mongoQuery).filter(
                (key) => mongoQuery[key] && mongoQuery[key].__duplicateField,
            );

            const cleanFilters: any = {};
            const duplicateConditions: any = {};

            Object.keys(mongoQuery).forEach((key) => {
                if (mongoQuery[key].__duplicateField) {
                    // This is a duplicate field - prepare for aggregation
                    duplicateConditions[key] = { $exists: true, $nin: [null, ""] };
                } else {
                    // Regular filter
                    cleanFilters[key] = mongoQuery[key];
                }
            });

            // Build aggregation pipeline for duplicate detection
            const pipeline: any[] = [
                // Match base filters and duplicate conditions
                {
                    $match: {
                        companyId: user.companyId,
                        deleted,
                        ...cleanFilters,
                        ...duplicateConditions,
                    },
                },
            ];

            const sortObj = {};

            // For each duplicate field, group and filter for duplicates
            duplicateFields.forEach((field) => {
                sortObj[field] = 1;
                pipeline.push(
                    // Group by the duplicate field
                    {
                        $group: {
                            _id: `$${field}`,
                            count: { $sum: 1 },
                            contacts: { $push: "$$ROOT" },
                        },
                    },
                    // Only keep groups with more than 1 contact (duplicates)
                    {
                        $match: {
                            count: { $gt: 1 },
                        },
                    },
                    // Unwind to get individual contacts back
                    {
                        $unwind: "$contacts",
                    },
                    // Replace root with the contact document
                    {
                        $replaceRoot: { newRoot: "$contacts" },
                    },
                );
            });

            sortObj["createdAt"] = -1;

            // Add search filter if provided
            if (search) {
                pipeline.push({
                    $match: {
                        $or: [
                            { fullName: { $regex: search, $options: "i" } },
                            { businessName: { $regex: search, $options: "i" } },
                            { firstName: { $regex: search, $options: "i" } },
                            { lastName: { $regex: search, $options: "i" } },
                            { street: { $regex: search, $options: "i" } },
                            { city: { $regex: search, $options: "i" } },
                            { state: { $regex: search, $options: "i" } },
                            { zip: { $regex: search, $options: "i" } },
                            { phone: { $regex: search, $options: "i" } },
                            { email: { $regex: search, $options: "i" } },
                        ],
                    },
                });
            }

            // Sort by creation date
            pipeline.push(
                {
                    $lookup: {
                        from: "LeadSource",
                        localField: "leadSourceId",
                        foreignField: "_id",
                        as: "leadSource",
                        pipeline: [{ $project: { name: 1, comapnyId: 1, createdBy: 1 } }],
                    },
                },
                {
                    $unwind: {
                        path: "$leadSource",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                { $sort: sortObj },
                {
                    $project: {
                        fullName: 1,
                        businessName: 1,
                        isBusiness: 1,
                        phone: 1,
                        email: 1,
                        type: 1,
                        street: 1,
                        city: 1,
                        state: 1,
                        zip: 1,
                        leadSourceName: "$leadSource.name",
                        createdAt: 1,
                        dateReceived: 1,
                        firstName: 1,
                        lastName: 1,
                        tags: 1,
                    },
                },
                {
                    $facet: {
                        paginatedResults: [{ $skip: skipValue }, { $limit: limit }],
                        totalCount: [{ $count: "count" }],
                    },
                },
            );

            // Execute aggregation
            const result = await this.contactModel.aggregate(pipeline);
            const paginatedResults = result[0]?.paginatedResults;
            const totalCount = result[0]?.totalCount[0]?.count || 0;

            return new OkResponse({
                data: paginatedResults,
                pagination: {
                    page: skip,
                    limit,
                    totalItems: totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                },
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async findOne(id: string, user) {
        try {
            const [oppsComments, contact] = await Promise.all([
                this.opportunityModel.aggregate([
                    { $match: { contactId: id, companyId: user.companyId } },
                    {
                        $lookup: {
                            from: "CrmStage",
                            localField: "stage",
                            foreignField: "_id",
                            pipeline: [{ $project: { stageGroup: 1 } }],
                            as: "stageData",
                        },
                    },
                    { $unwind: { path: "$stageData", preserveNullAndEmptyArrays: true } },
                    {
                        $lookup: {
                            from: "Member",
                            let: {
                                memberArr: {
                                    $ifNull: [
                                        {
                                            $map: {
                                                input: { $ifNull: ["$comments", []] },
                                                as: "comment",
                                                in: "$$comment.createdBy",
                                            },
                                        },
                                        [],
                                    ],
                                },
                            },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: { $in: ["$_id", "$$memberArr"] },
                                    },
                                },
                                { $project: { _id: 1, name: 1 } },
                            ],
                            as: "users",
                        },
                    },
                    {
                        $project: {
                            oppId: "$_id",
                            comments: 1,
                            PO: 1,
                            num: 1,
                            stageGroup: "$stageData.stageGroup",
                            nextAction: 1,
                            users: 1,
                        },
                    },
                ]),
                this.contactModel
                    .aggregate([
                        {
                            $match: {
                                _id: id,
                                companyId: user.companyId,
                            },
                        },
                        {
                            $lookup: {
                                from: "Contact",
                                let: { referredBy: "$referredBy" },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: { $eq: ["$_id", "$$referredBy"] },
                                        },
                                    },
                                    { $project: { name: 1 } },
                                ],
                                as: "referrer",
                            },
                        },
                        {
                            $unwind: {
                                path: "$referrer",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "Member",
                                let: {
                                    memberArr: {
                                        $ifNull: [
                                            {
                                                $map: {
                                                    input: { $ifNull: ["$comments", []] },
                                                    as: "comment",
                                                    in: "$$comment.createdBy",
                                                },
                                            },
                                            [],
                                        ],
                                    },
                                },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: { $in: ["$_id", "$$memberArr"] },
                                        },
                                    },
                                    { $project: { _id: 1, name: 1 } },
                                ],
                                as: "users",
                            },
                        },
                    ])
                    .then((res) => res[0]),
            ]);

            const commentsFromOpps = oppsComments.flatMap((opp) =>
                (opp.comments || []).map((comment) => ({
                    oppId: opp.oppId,
                    ...comment,
                    PO: opp.PO,
                    num: opp.num,
                    stageGroup: opp.stageGroup,
                })),
            );
            contact.oppsComments = commentsFromOpps;
            contact.oppnextAction = oppsComments.map((opp) => opp.nextAction);

            if (!contact) {
                throw new NotFoundException("Contact not found");
            }

            const allUsers = new Map();
            for (const user of contact.users ?? []) {
                allUsers.set(user._id, user);
            }

            // Add oppsComments.users (each opp from oppsComments array has `users`)
            for (const opp of oppsComments) {
                for (const user of opp.users ?? []) {
                    allUsers.set(user._id, user);
                }
            }
            const usersMap = allUsers;
            // Modify comments for name
            if (contact && contact.comments && contact.comments.length > 0) {
                contact.comments.forEach((c) => {
                    const user: any = usersMap.get(c.createdBy);
                    if (user) {
                        c.name = user.name;
                    }
                });
            }
            if (contact.oppsComments && contact.oppsComments.length > 0) {
                contact.oppsComments.forEach((c) => {
                    const user: any = usersMap.get(c.createdBy);
                    if (user) {
                        c.name = user.name;
                    }
                    contact.comments.push(c);
                });
            }

            return new OkResponse({ contact });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async update(id: string, updateContactDto: CreateContactDto, user) {
        try {
            const contact = await this.contactModel.findOneAndUpdate(
                {
                    _id: id,
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $set: updateContactDto,
                },
                { new: true },
            );

            if (!contact) {
                throw new NotFoundException("Contact not found");
            }

            return new OkResponse({ contact });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async remove(id: string, user) {
        try {
            const contact = await this.contactModel.findOneAndUpdate(
                {
                    _id: id,
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $set: { deleted: true },
                },
                { new: true },
            );

            if (!contact) {
                throw new NotFoundException("Contact not found");
            }

            return new OkResponse({ message: "Contact deleted successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async removeMultipleContacts(ids: string[], user) {
        try {
            const result = await this.contactModel.updateMany(
                {
                    _id: { $in: ids },
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $set: { deleted: true },
                },
            );

            if (result.modifiedCount !== ids.length) {
                throw new BadRequestException("Failed to delete all contacts");
            }

            return new OkResponse({ message: "Contacts deleted successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDelete(id: string, user) {
        try {
            const contact = await this.contactModel.findOneAndDelete({
                _id: id,
                companyId: user.companyId,
                deleted: true,
            });

            if (!contact) {
                throw new NotFoundException("Contact not found");
            }

            return new OkResponse({ message: "Contact permanently deleted" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restore(id: string, user) {
        try {
            const contact = await this.contactModel.findOneAndUpdate(
                {
                    _id: id,
                    companyId: user.companyId,
                    deleted: true,
                },
                {
                    $set: { deleted: false },
                },
                { new: true },
            );

            if (!contact) {
                throw new NotFoundException("Contact not found");
            }

            return new OkResponse({ message: "Contact restored successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreMultipleContacts(ids: string[], user) {
        try {
            const result = await this.contactModel.updateMany(
                {
                    _id: { $in: ids },
                    companyId: user.companyId,
                    deleted: true,
                },
                {
                    $set: { deleted: false },
                },
            );

            if (result.modifiedCount !== ids.length) {
                throw new BadRequestException("Failed to restore all contacts");
            }

            return new OkResponse({ message: "Contacts restored successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addLinkedContact(id: string, linkedContactDtos: AddLinkedContactDto[], user) {
        try {
            const linkedContacts = linkedContactDtos.map((dto) => ({
                id: dto.id,
                relationship: dto.relationship,
            }));

            const { modifiedCount } = await this.contactModel.updateOne(
                {
                    _id: id,
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $push: {
                        linkedContacts: { $each: linkedContacts },
                    },
                },
            );

            if (!modifiedCount) {
                throw new BadRequestException("Failed to add linked contacts");
            }

            return new OkResponse({ message: "Linked contacts added successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getLinkedContact(id: string, user) {
        try {
            const [contact, relatedContacts] = await Promise.all([
                this.contactModel
                    .findOne(
                        {
                            _id: id,
                            companyId: user.companyId,
                            deleted: false,
                        },
                        { linkedContacts: 1 },
                    )
                    .populate(
                        "linkedContacts.id",
                        "fullName phone email notes fullAddress street city state zip",
                        "Contact",
                    ),
                this.contactModel
                    .find({
                        "linkedContacts.id": id,
                        companyId: user.companyId,
                        deleted: false,
                    })
                    .select("fullName phone email notes fullAddress street city state zip"),
            ]);

            // Combine both direct and reverse linked contacts
            const allLinkedContacts = [
                ...(contact?.linkedContacts || []),
                ...relatedContacts.map((c) => ({
                    id: {
                        _id: c._id,
                        fullName: c.fullName,
                        phone: c.phone,
                        email: c.email,
                        notes: c.notes,
                        fullAddress: c.fullAddress,
                        street: c.street,
                        city: c.city,
                        state: c.state,
                        zip: c.zip,
                    },
                    relationship: "Parent Contact",
                })),
            ];

            return new OkResponse({ linkedContacts: allLinkedContacts });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async removeLinkedContact(id: string, linkedContactId: string, user) {
        try {
            const { modifiedCount } = await this.contactModel.updateOne(
                {
                    _id: id,
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $pull: {
                        linkedContacts: { id: linkedContactId },
                    },
                },
            );

            if (!modifiedCount) {
                throw new BadRequestException("Failed to remove linked contact");
            }

            return new OkResponse({ message: "Linked contact removed successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async searchContacts(dto: GetSearchContactDto, user) {
        try {
            const { limit = 10, skip = 1, type, search, fields } = dto;
            const skipValue = (skip - 1) * limit;

            const searchRegex = new RegExp(search, "i");
            const project = fields
                ? JSON.parse(fields)
                : {
                      //   firstName: 1,
                      //   lastName: 1,
                      fullName: 1,
                      //   businessName: 1,
                      //   isBusiness: 1,
                      //   phone: 1,
                      //   email: 1,
                      //   fullAddress: 1,
                      //   notes: 1,
                  };

            const query = {
                companyId: user.companyId,
                deleted: false,
                ...(type && { type }),
                $or: [
                    { firstName: searchRegex },
                    { lastName: searchRegex },
                    { fullName: searchRegex },
                    { businessName: searchRegex },
                    { phone: searchRegex },
                    { email: searchRegex },
                ],
            };

            const contacts = await this.contactModel
                .find(query)
                .select(project)
                .sort({ createdAt: -1 })
                .skip(skipValue)
                .limit(limit);

            return new OkResponse({ contacts });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateDnd(id: string, updateDndDto: UpdateDndDto, user) {
        try {
            const { modifiedCount } = await this.contactModel.updateOne(
                {
                    _id: id,
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $set: { dnd: updateDndDto },
                },
            );

            if (!modifiedCount) {
                throw new BadRequestException("Failed to update DND settings");
            }

            return new OkResponse({ message: "DND settings updated successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addTagsInMultipleContacts(ids: string[], tags: string[], user) {
        try {
            const { modifiedCount } = await this.contactModel.updateMany(
                {
                    _id: { $in: ids },
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $addToSet: {
                        tags: { $each: tags },
                    },
                },
            );

            if (modifiedCount !== ids.length) {
                throw new BadRequestException("Failed to add tags to all contacts");
            }

            return new OkResponse({ message: "Tags added successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async removeTagsInMultipleContacts(ids: string[], tags: string[], user) {
        try {
            const { modifiedCount } = await this.contactModel.updateMany(
                {
                    _id: { $in: ids },
                    companyId: user.companyId,
                    deleted: false,
                },
                {
                    $pull: {
                        tags: { $in: tags },
                    },
                },
            );

            if (modifiedCount !== ids.length) {
                throw new BadRequestException("Failed to remove tags from all contacts");
            }

            return new OkResponse({ message: "Tags removed successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getContactOpportunities(contactId: string, user) {
        try {
            const [contact, relatedContacts] = await Promise.all([
                this.contactModel.findOne(
                    {
                        _id: contactId,
                        companyId: user.companyId,
                        deleted: false,
                    },
                    { linkedContacts: 1 },
                ),
                this.contactModel.find(
                    {
                        linkedContacts: { $elemMatch: { id: contactId } },
                        companyId: user.companyId,
                        deleted: false,
                    },
                    { linkedContacts: 1 },
                ),
            ]);

            // Collect all relevant contact IDs
            const contactIds = [
                contactId,
                ...relatedContacts.map((c) => c._id),
                ...(contact?.linkedContacts?.map((c) => c.id) || []), // Safe access for linkedContacts
            ].filter((id, index, self) => id && self.indexOf(id) === index); // Remove duplicates

            const clientOpps = await this.opportunityModel.aggregate([
                {
                    $match: {
                        companyId: user.companyId,
                        contactId: { $in: contactIds },
                        deleted: false,
                    },
                },
                {
                    $lookup: {
                        from: "ProjectType",
                        localField: "oppType",
                        foreignField: "_id",
                        as: "oppType",
                    },
                },
                { $unwind: { path: "$oppType", preserveNullAndEmptyArrays: true } },

                {
                    $lookup: {
                        from: "CrmStage",
                        localField: "stage",
                        foreignField: "_id",
                        as: "stage",
                    },
                },
                { $unwind: { path: "$stage", preserveNullAndEmptyArrays: true } },

                {
                    $lookup: {
                        from: "Contact",
                        localField: "contactId",
                        foreignField: "_id",
                        as: "contactId",
                    },
                },
                { $unwind: { path: "$contactId", preserveNullAndEmptyArrays: true } },

                {
                    $project: {
                        _id: 1,
                        PO: 1,
                        num: 1,
                        companyId: 1,
                        firstName: 1,
                        lastName: 1,
                        street: 1,
                        city: 1,
                        state: 1,
                        soldValue: 1,
                        realRevValue: 1,
                        contactId: 1,
                        stage: 1,
                        oppDate: 1,
                        oppType: { _id: "$oppType._id", name: "$oppType.name" },
                        fullName: {
                            $cond: {
                                if: { $eq: ["$contactId.isBusiness", true] },
                                then: "$contactId.businessName",
                                else: "$contactId.fullName",
                            },
                        },
                    },
                },
                { $sort: { createdAt: -1 } },
            ]);

            return new OkResponse({ clientOpps });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async migrateContactAndOpp(fromContact: string, toContact: string, user) {
        const session = await this.connection.startSession();
        session.startTransaction();

        try {
            // Update opportunities and old client in parallel
            const [oldClient, newClient] = await Promise.all([
                this.contactModel.findOneAndUpdate(
                    { companyId: user.companyId, _id: fromContact },
                    { $set: { deleted: true } },
                    { session },
                ),
                this.contactModel.findOne({ _id: toContact }),
                this.opportunityModel.updateMany(
                    { companyId: user.companyId, contactId: fromContact },
                    { $set: { contactId: toContact } },
                    { session },
                ),
                this.leadModel.updateMany(
                    { companyId: user.companyId, contactId: fromContact },
                    { $set: { contactId: toContact } },
                    { session },
                ),
                this.projectModel.updateMany(
                    { companyId: user.companyId, contactId: fromContact },
                    { $set: { contactId: toContact } },
                    { session },
                ),
                this.priceModel.updateMany(
                    { companyId: user.companyId, contactId: fromContact },
                    { $set: { contactId: toContact } },
                    { session },
                ),
            ]);

            if (!oldClient || !newClient) {
                throw new BadRequestException(oldClient ? "New client not found." : "Old client not found.");
            }

            // Update new contact with old client's address, phone, email, etc in comment of current contact
            const body = `Merged with ${oldClient.fullName} -> Phone: ${oldClient.phone}, Email: ${oldClient.email}, Address: ${oldClient.fullAddress}`;
            const updateResult = await this.contactModel.updateOne(
                { companyId: user.companyId, _id: toContact },
                {
                    $push: {
                        comments: {
                            _id: randomUUID(),
                            body,
                            createdBy: user.memberId,
                            createdAt: new Date(),
                            edits: [],
                        },
                    },
                },
                { session },
            );

            if (updateResult.modifiedCount === 0) {
                throw new BadRequestException("Client update failed.");
            }

            await session.commitTransaction();
            return new OkResponse({ message: "Opportunities migrated successfully!" });
        } catch (error: any) {
            await session.abortTransaction();
            throw error instanceof HttpException ? error : new InternalServerErrorException(error.message);
        } finally {
            session.endSession();
        }
    }

    async updateContactStatus(contactId: string, newStatus: string) {
        try {
            const result = await this.contactModel.updateOne(
                { _id: contactId },
                {
                    $set: {
                        status: newStatus,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Lead status changed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getImportedContacts(companyId: string, paginationDto: PaginationDto) {
        try {
            const limit = paginationDto.limit || 10;
            const skipValue = (paginationDto.skip - 1) * limit;

            // Get contact IDs from leads and fetch contacts in a single aggregation pipeline
            const contacts = await this.leadModel.aggregate([
                {
                    $match: {
                        companyId,
                        zapierLead: true,
                        rawTracking: { $exists: true, $ne: {}, $type: "object" },
                    },
                },
                { $project: { contactId: 1, rawTracking: 1, createdAt: 1 } },
                {
                    $lookup: {
                        from: "Contact",
                        localField: "contactId",
                        foreignField: "_id",
                        pipeline: [
                            {
                                $project: {
                                    fullName: 1,
                                    email: 1,
                                    phone: 1,
                                    // createdAt: 1,
                                    businessName: 1,
                                    isBusiness: 1,
                                },
                            },
                        ],
                        as: "contact",
                    },
                },
                { $unwind: { path: "$contact", preserveNullAndEmptyArrays: true } },
                {
                    $addFields: {
                        contactName: {
                            $cond: [
                                { $eq: ["$contact.isBusiness", true] },
                                "$contact.businessName",
                                "$contact.fullName",
                            ],
                        },
                    },
                },
                { $sort: { createdAt: -1 } },
                {
                    $project: {
                        contactId: 1,
                        rawTracking: 1,
                        contactName: 1,
                        fullName: "$contact.fullName",
                        email: "$contact.email",
                        phone: "$contact.phone",
                        createdAt: 1,
                    },
                },
                {
                    $facet: {
                        paginatedResults: [{ $skip: skipValue }, { $limit: limit }],
                        totalCount: [{ $count: "count" }],
                    },
                },
            ]);
            const paginatedResults = contacts[0].paginatedResults;
            const totalCount = contacts[0].totalCount[0]?.count || 0;

            return new OkResponse({
                contacts: paginatedResults,
                pagination: {
                    page: paginationDto.skip,
                    limit,
                    totalItems: totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                },
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // comments section
    async addComment(contactId: string, createContactCommentDto: CreateContactCommentDto) {
        try {
            const result = await this.contactModel.updateOne(
                { _id: contactId },
                {
                    $push: {
                        comments: {
                            _id: randomUUID(),
                            body: createContactCommentDto.body,
                            createdBy: createContactCommentDto.memberId,
                            createdAt: new Date(createContactCommentDto.currDate),
                            edits: [],
                        },
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new CreatedResponse({ message: "Comment created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateComment(user: any, contactId: string, commentId: string, body: string) {
        try {
            const { memberId, companyId } = user;
            const contact = await this.contactModel
                .findOne({
                    _id: contactId,
                    companyId,
                })
                .select("comments");
            if (!contact) throw new HttpException("contact not found", HttpStatus.BAD_REQUEST);

            const memberComment = contact.comments.find((c) => c._id === commentId);

            if (memberComment && memberComment.createdBy !== memberId)
                throw new BadRequestException("You can only edit your comments");

            if (memberComment && memberComment?.edits?.length >= 5)
                throw new BadRequestException("Maximum numbers of edit limit reached");

            if (
                memberComment &&
                Math.abs(new Date(memberComment?.createdAt).getTime() - new Date().getTime()) > 60 * 60 * 1000
            )
                throw new BadRequestException("You can't edit old comment");

            const changes = getTextChange({ body }, { body: memberComment.body });

            const edits: any[] = changes?.body
                ? [{ editedAt: new Date(), edit: changes?.body }, ...memberComment?.edits]
                : [...memberComment?.edits];

            const result = await this.contactModel.updateOne(
                { _id: contactId, "comments._id": commentId, "comments.createdBy": memberId },
                {
                    $set: {
                        "comments.$.body": body,
                        "comments.$.editedAt": new Date(),
                        "comments.$.edits": edits,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Comment updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteComment(contactId: string, commentId: string, memberId: string) {
        try {
            const result = await this.contactModel.updateOne(
                {
                    _id: contactId,
                    comments: {
                        $elemMatch: {
                            _id: commentId,
                            createdBy: memberId,
                        },
                    },
                },
                {
                    $pull: {
                        comments: { _id: commentId },
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Comment deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getComments(contactId: string) {
        try {
            const contact = await this.contactModel.aggregate([
                {
                    $match: {
                        _id: contactId,
                    },
                },
                {
                    $lookup: {
                        from: "Member",
                        localField: "comments.createdBy",
                        foreignField: "_id",
                        as: "userDetails",
                    },
                },
                {
                    $project: {
                        comments: {
                            $map: {
                                input: "$comments",
                                as: "comment",
                                in: {
                                    $mergeObjects: [
                                        "$$comment",
                                        {
                                            name: {
                                                $let: {
                                                    vars: {
                                                        user: {
                                                            $arrayElemAt: [
                                                                {
                                                                    $filter: {
                                                                        input: "$userDetails",
                                                                        as: "user",
                                                                        cond: {
                                                                            $eq: [
                                                                                "$$user._id",
                                                                                "$$comment.createdBy",
                                                                            ],
                                                                        },
                                                                    },
                                                                },
                                                                0,
                                                            ],
                                                        },
                                                    },
                                                    in: {
                                                        $ifNull: ["$$user.name", "$$comment.createdBy"],
                                                    },
                                                },
                                            },
                                        },
                                    ],
                                },
                            },
                        },
                    },
                },
            ]);

            return new OkResponse({ comments: contact[0]?.comments || [] });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // actions
    async createNewAction(
        companyId: string,
        memberId: string,
        contactId: string,
        createNewActionDto: CreateNewActionDto,
    ) {
        try {
            // console.log(createNewActionDto);
            const result = await this.contactModel.updateOne(
                { _id: contactId, companyId },
                {
                    $set: {
                        nextAction: {
                            _id: createNewActionDto.id,
                            type: createNewActionDto.type,
                            body: createNewActionDto.body,
                            due: createNewActionDto.dueDate,
                            assignTo: createNewActionDto.assignTo,
                            createdBy: memberId,
                            createdAt: createNewActionDto.currDate,
                        },
                        // todoCheck: false,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new CreatedResponse({ message: "Action created successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async completeAction(
        companyId: string,
        memberId: string,
        contactId: string,
        createNewActionDto: CreateNewActionDto,
    ) {
        try {
            const result = await this.contactModel.updateOne(
                { _id: contactId, companyId, "actions._id": { $ne: createNewActionDto.id } }, // Ensures ID is not already in actions
                {
                    $push: {
                        actions: {
                            _id: createNewActionDto.id,
                            type: createNewActionDto.type,
                            body: createNewActionDto.body,
                            due: createNewActionDto.dueDate,
                            assignTo: createNewActionDto.assignTo,
                            completedBy: memberId,
                            completedAt: createNewActionDto.currDate,
                        },
                    },
                    // todoCheck: true,
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Action completed successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getActions(contactId: string) {
        try {
            const contact = await this.contactModel.findOne(
                { _id: contactId },
                { actions: 1, nextAction: 1 },
            );

            return new OkResponse({ actions: contact?.actions, nextAction: contact?.nextAction });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteAction(contactId: string, actionId: string) {
        try {
            const result = await this.contactModel.updateOne(
                { _id: contactId },
                {
                    $pull: {
                        actions: { _id: actionId },
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Action deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateNextAction(contactId: string, nextAction: any) {
        try {
            const result = await this.contactModel.updateOne(
                { _id: contactId },
                {
                    $set: {
                        nextAction,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
            }

            return new OkResponse({ message: "Next action updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // activity
    async updateContactActivity(
        companyId: string,
        memberId: string,
        updateContactActivityDto: UpdateContactActivityDto,
    ) {
        try {
            const activity = await this.activityLogModel.updateOne(
                { moduleId: updateContactActivityDto.id, moduleType: "contact", companyId },
                {
                    $push: {
                        activities: {
                            _id: randomUUID(),
                            body: updateContactActivityDto.body,
                            createdBy: memberId,
                            createdAt: updateContactActivityDto.currDate,
                        },
                    },
                },
                { upsert: true, new: true },
            );

            return new OkResponse({ data: activity, message: "Contact activity updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getContactActivity(contactId: string) {
        try {
            const allData = await this.activityLogModel.aggregate([
                { $match: { moduleId: contactId } },
                {
                    $lookup: {
                        from: "Member",
                        localField: "activities.createdBy",
                        foreignField: "_id",
                        as: "userDetails",
                    },
                },
                {
                    $project: {
                        _id: 1,
                        companyId: 1,
                        oppId: 1,
                        activities: {
                            $map: {
                                input: "$activities",
                                as: "act",
                                in: {
                                    $mergeObjects: [
                                        "$$act",
                                        {
                                            name: {
                                                $let: {
                                                    vars: {
                                                        user: {
                                                            $arrayElemAt: [
                                                                {
                                                                    $filter: {
                                                                        input: "$userDetails",
                                                                        as: "user",
                                                                        cond: {
                                                                            $eq: [
                                                                                "$$user._id",
                                                                                "$$act.createdBy",
                                                                            ],
                                                                        },
                                                                    },
                                                                },
                                                                0,
                                                            ],
                                                        },
                                                    },
                                                    in: {
                                                        $ifNull: ["$$user.name", "$$act.createdBy"],
                                                    },
                                                },
                                            },
                                        },
                                    ],
                                },
                            },
                        },
                        createdAt: 1,
                        updatedAt: 1,
                    },
                },
            ]);

            if (!allData.length) {
                return new OkResponse({ activities: [] });
            }

            return new OkResponse({ activities: allData[0].activities });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // lead
    async getContactOfTypeLeads(status: string, deleted: boolean, user) {
        try {
            const query = deleted
                ? {
                      companyId: user.companyId,
                      deleted,
                      type: ContactTypeEnum.LEAD,
                  }
                : {
                      companyId: user.companyId,
                      status,
                      deleted,
                      type: ContactTypeEnum.LEAD,
                  };
            const leads = await this.contactModel
                .find(query)
                .select("fullName businessName isBusiness csrId stageId newLeadDate createdAt");

            return new OkResponse({ leads });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // async lostContact(contactId: string, memberId: string, lostContactDto: LostAndUnLostContactDto) {
    //     try {
    //         const result = await this.contactModel.updateOne(
    //             { _id: contactId },
    //             {
    //                 $set: {
    //                     lostDate: lostContactDto.date,
    //                     lostReason: lostContactDto.reason,
    //                     lostBy: memberId,
    //                     status: "lost",
    //                 },
    //             },
    //         );

    //         if (result.modifiedCount === 0) {
    //             throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
    //         }

    //         return new OkResponse({ message: "Lead lost successfully!" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    // async unLostContact(contactId: string, memberId: string, unLostContactDto: LostAndUnLostContactDto) {
    //     try {
    //         const result = await this.contactModel.updateOne(
    //             { _id: contactId },
    //             {
    //                 $set: {
    //                     unLostReason: unLostContactDto.reason,
    //                     unLostDate: unLostContactDto.date,
    //                     unLostBy: memberId,
    //                     status: "active",
    //                 },
    //             },
    //         );

    //         if (result.modifiedCount === 0) {
    //             throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
    //         }

    //         return new OkResponse({ message: "Contact un-lost successfully!" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    // async updateLeadStage(contactId: string, newStage: string) {
    //     try {
    //         const result = await this.contactModel.updateOne(
    //             { _id: contactId },
    //             {
    //                 $set: {
    //                     stageId: newStage,
    //                 },
    //             },
    //         );

    //         if (result.modifiedCount === 0) {
    //             throw new HttpException("Failed to update changes!", HttpStatus.BAD_REQUEST);
    //         }

    //         return new OkResponse({ message: "Lead stage changed successfully!" });
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    async getLeadByContactId(contactId: string, companyId: string) {
        try {
            const leads = await this.leadModel.find({ contactId, companyId }).sort({ newLeadDate: -1 });
            return new OkResponse({ leads });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // migration scripts
    // async migrateClientToContact() {
    //     try {
    //         console.log("started");
    //         const clients = await this.clientModel.find({});
    //         console.log("migrateClientToContact", clients.length);

    //         const contactsToCreate = clients.map((client) => {
    //             const type = client.status === "Prospect" ? ContactTypeEnum.PROSPECT : ContactTypeEnum.CLIENT;

    //             const fullName = client?.lastName
    //                 ? `${client.firstName} ${client.lastName}`
    //                 : client.firstName;
    //             const businessName = client.isBusiness ? fullName : undefined;
    //             console.log(client.leadSource);
    //             const addressParts = [client?.street, client?.city, client?.state, client?.zip].filter(
    //                 Boolean,
    //             );
    //             const fullAddress = addressParts.length > 0 ? addressParts.join(", ") : undefined;

    //             const contact = {
    //                 _id: client._id,
    //                 fullName,
    //                 firstName: client.firstName,
    //                 lastName: client?.lastName,
    //                 isBusiness: client.isBusiness,
    //                 businessName: businessName ? businessName.trim() : undefined,
    //                 phone: client.phone,
    //                 email: client.email,
    //                 companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
    //                 leadSourceId:
    //                     client?.leadSource &&
    //                     /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
    //                         client.leadSource,
    //                     )
    //                         ? client.leadSource
    //                         : undefined,
    //                 type: type,
    //                 dateReceived: client.createdAt || new Date(),
    //                 street: client.street,
    //                 city: client.city,
    //                 state: client.state,
    //                 zip: client.zip,
    //                 fullAddress,
    //                 notes: client.notes,
    //                 referredBy: client?.referredBy,
    //                 createdAt: client.createdAt,
    //             };

    //             // Filter out undefined values
    //             return Object.fromEntries(
    //                 Object.entries(contact).filter(([_, value]) => value !== undefined),
    //             );
    //         });

    //         await this.contactModel.insertMany(contactsToCreate);

    //         const linkedContactsToCreate = [];
    //         const linkedContactsToUpdate = [];

    //         for (const client of clients) {
    //             if (client.contacts && client.contacts.length > 0) {
    //                 for (const contact of client.contacts) {
    //                     const fullName = client?.lastName
    //                         ? `${client.firstName} ${client.lastName}`
    //                         : client.firstName;

    //                     const cfullName = contact?.lastName
    //                         ? `${contact?.firstName} ${contact.lastName}`
    //                         : contact?.firstName
    //                         ? contact?.firstName
    //                         : fullName;

    //                     const linkedContact = {
    //                         _id: randomUUID(),
    //                         firstName: contact?.firstName || client.firstName,
    //                         lastName: contact?.lastName,
    //                         fullName: cfullName,
    //                         phone: contact?.phone,
    //                         email: contact?.email,
    //                         companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
    //                         type: ContactTypeEnum.LINKED,
    //                         dateReceived: client.createdAt,
    //                         createdAt: client.createdAt,
    //                     };

    //                     linkedContactsToCreate.push(linkedContact);
    //                     linkedContactsToUpdate.push({
    //                         updateOne: {
    //                             filter: { _id: client._id },
    //                             update: {
    //                                 $push: {
    //                                     linkedContacts: {
    //                                         id: linkedContact._id,
    //                                         relationship: "Other",
    //                                     },
    //                                 },
    //                             },
    //                         },
    //                     });
    //                 }
    //             }
    //         }

    //         if (linkedContactsToCreate.length > 0) {
    //             await this.contactModel.insertMany(linkedContactsToCreate);
    //         }

    //         if (linkedContactsToUpdate.length > 0) {
    //             await this.contactModel.bulkWrite(linkedContactsToUpdate);
    //         }

    //         console.log("Client data migrated to Contacts successfully");
    //     } catch (error) {
    //         console.log(error);
    //         throw new Error(`Migration failed: ${error.message}`);
    //     }
    // }

    // async migrateLeadToContact() {
    //     try {
    //         const leads = await this.leadModel.find({ oppId: { $exists: false } });

    //         console.log("Starting contact to lead migration", leads.length);
    //         const contactsToCreate = leads.map((lead: any) => {
    //             // Determine status based on oppId
    //             const type = lead.oppId ? ContactTypeEnum.PROSPECT : ContactTypeEnum.LEAD;
    //             const status = lead.lost ? "lost" : lead.status;

    //             const fullName = lead?.lastName ? `${lead.firstName} ${lead.lastName}` : lead.firstName;

    //             const addressParts = [lead.street, lead.city, lead.state, lead.zip].filter(Boolean);
    //             const fullAddress = addressParts.length > 0 ? addressParts.join(", ") : undefined;

    //             // Build tracking attribution if exists
    //             const trackingAttribution =
    //                 lead["Conversion - Campaign"] !== "" ||
    //                 lead["Conversion - Ad Set"] !== "" ||
    //                 lead["Conversion - Creative"] !== "" ||
    //                 lead["Conversion - Keywords"] !== "" ||
    //                 lead["Google - Click ID"] !== "" ||
    //                 lead.contact_source !== ""
    //                     ? {
    //                           sessionSource: lead.contact_source || undefined,
    //                           utmSource: lead["Lead Source Description"] || undefined,
    //                           utmMedium: "",
    //                           utmContent: "",
    //                           utmCampaign: lead["Conversion - Campaign"] || undefined,
    //                           utmTerm: "",
    //                           utmKeyword: lead["Conversion - Keywords"] || undefined,
    //                           utmMatchType: "",
    //                           referringWebpage: "",
    //                           googleClickId: lead["Google - Click ID"] || undefined,
    //                           adSetId: lead["Conversion - Ad Set"] || undefined,
    //                           adName: lead["Conversion - Creative"] || undefined,
    //                           gaClientId: "",
    //                           userAgent: "",
    //                           url: "",
    //                           ip: "",
    //                           adGroupId: "",
    //                           gbraId: "",
    //                           wbraId: "",
    //                           fbr: "",
    //                           fbp: "",
    //                           form: "",
    //                           formId: lead["Form ID"] || undefined,
    //                           createdAt: Date,
    //                       }
    //                     : undefined;

    //             const comments = lead.comments.length > 0 ? lead.comments : [];
    //             if (lead["Contact Notes"] && lead["Contact Notes"] !== "") {
    //                 comments.push({
    //                     _id: randomUUID(),
    //                     body: lead["Contact Notes"],
    //                     createdBy: lead.createdBy || undefined,
    //                     createdAt: lead.createdAt || new Date(),
    //                     edits: [],
    //                 });
    //             }
    //             if (lead["Project Notes"] && lead["Project Notes"] !== "") {
    //                 comments.push({
    //                     _id: randomUUID(),
    //                     body: lead["Project Notes"] || undefined,
    //                     createdBy: lead.createdBy || undefined,
    //                     createdAt: lead.createdAt || new Date(),
    //                     edits: [],
    //                 });
    //             }

    //             // Build base contact object
    //             const contact = {
    //                 _id: lead._id,
    //                 companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
    //                 type,
    //                 isBusiness: false,
    //                 firstName: lead.firstName || undefined,
    //                 lastName: lead.lastName || undefined,
    //                 fullName,
    //                 phone: lead.phone || undefined,
    //                 email: lead.email || undefined,
    //                 street: lead.street || undefined,
    //                 city: lead.city || undefined,
    //                 state: lead.state || undefined,
    //                 zip: lead.zip || undefined,
    //                 fullAddress,
    //                 stageId: lead.stage || undefined,
    //                 csrId: lead.csrId || undefined,
    //                 status,
    //                 createdBy: lead.createdBy || undefined,
    //                 dateReceived: lead.createdAt || new Date(),
    //                 zapierLead: lead.zapierLead || false,
    //                 createdAt: lead.createdAt || new Date(),
    //                 deleted: lead.deleted || false,
    //                 notes: lead.notes || undefined,
    //                 leadSourceId: lead.leadSourceId || undefined,
    //                 newLeadDate: lead.newLeadDate || undefined,
    //                 actions: lead.actions || undefined,
    //                 nextAction: lead.nextAction || undefined,
    //                 // todoCheck: lead.todoCheck || undefined,
    //                 comments,
    //                 // stepsChecklist: lead.stepsChecklist || undefined,
    //                 oppId: lead.oppId || undefined,
    //                 oppDate: lead.oppDate || undefined,
    //                 referredBy: lead?.referredBy || undefined,
    //             };

    //             // Add tracking attribution if exists
    //             if (trackingAttribution) {
    //                 contact["trackingAttribution"] = trackingAttribution;
    //             }

    //             // Filter out undefined values
    //             return Object.fromEntries(
    //                 Object.entries(contact).filter(([_, value]) => value !== undefined),
    //             );
    //         });
    //         // const leads = await this.leadModel.find({});
    //         console.log("migrateLeadToContact", leads.length);

    //         await this.contactModel.insertMany(contactsToCreate);

    //         console.log("Successfully migrated contact to lead");
    //         return new OkResponse({ message: "Migration completed successfully" });
    //     } catch (error) {
    //         console.error("Migration failed:", error);
    //         throw new InternalServerErrorException("Failed to migrate contact to lead");
    //     }
    // }

    // async migrateClientIdToContactId() {
    //     try {
    //         console.log("Starting clientId to contactId migration");

    //         // Update opportunities collection
    //         await this.opportunityModel.updateMany(
    //             { clientId: { $exists: true } },
    //             { $rename: { clientId: "contactId" } },
    //         );

    //         // Update projects collection
    //         await this.projectModel.updateMany(
    //             { clientId: { $exists: true } },
    //             { $rename: { clientId: "contactId" } },
    //         );

    //         // Update prices collection
    //         await this.priceModel.updateMany(
    //             { clientId: { $exists: true } },
    //             { $rename: { clientId: "contactId" } },
    //         );

    //         console.log("Successfully migrated clientId to contactId in all collections");
    //         return new OkResponse({ message: "Migration completed successfully" });
    //     } catch (error) {
    //         console.error("Migration failed:", error);
    //         throw new InternalServerErrorException("Failed to migrate clientId to contactId");
    //     }
    // }

    // async migrateOpportunityClientIdToContactId() {
    //     try {
    //         console.log("Starting opportunity clientId to contactId migration");

    //         // Find all opportunities with clientId but no contactId
    //         const opportunities = await this.opportunityModel.find(
    //             {
    //                 clientId: { $exists: true },
    //             },
    //             { _id: 1, clientId: 1 },
    //         );
    //         const projects = await this.projectModel.find(
    //             {
    //                 clientId: { $exists: true },
    //             },
    //             { _id: 1, clientId: 1 },
    //         );
    //         const prices = await this.priceModel.find(
    //             {
    //                 clientId: { $exists: true },
    //             },
    //             { _id: 1, clientId: 1 },
    //         );
    //         console.log(opportunities.length, projects.length, prices.length);
    //         // Update each opportunity
    //         const updatePromises = opportunities.map((opportunity) => {
    //             return this.opportunityModel.updateOne(
    //                 { _id: opportunity._id },
    //                 {
    //                     $set: { contactId: opportunity.clientId },
    //                 },
    //             );
    //         });
    //         const projectPromises = projects.map((project) => {
    //             return this.projectModel.updateOne(
    //                 { _id: project._id },
    //                 {
    //                     $set: { contactId: project.clientId },
    //                 },
    //             );
    //         });
    //         const pricePromises = prices.map((price) => {
    //             return this.priceModel.updateOne(
    //                 { _id: price._id },
    //                 {
    //                     $set: { contactId: price.clientId },
    //                 },
    //             );
    //         });

    //         await Promise.all(projectPromises);
    //         await Promise.all(pricePromises);
    //         await Promise.all(updatePromises);

    //         console.log(
    //             `Successfully migrated ${opportunities.length} opportunities from clientId to contactId`,
    //         );
    //     } catch (error) {
    //         console.error("Opportunity migration failed:", error);
    //         throw new InternalServerErrorException("Failed to migrate opportunity clientId to contactId");
    //     }
    // }

    // async migrateReferrerToContact() {
    //     try {
    //         console.log("Starting referrer to contact migration");

    //         // Find all contacts with referrer
    //         const refferrers = await this.referrersModel.find();
    //         console.log(refferrers.length);

    //         // create contact for refferrers
    //         const contactsToCreate = refferrers.map((referrer) => {
    //             const firstName = referrer.name.split(" ")[0];
    //             const lastName = referrer.name.split(" ").slice(1).join(" ") || "";
    //             console.log(referrer.name, firstName, lastName);
    //             return {
    //                 _id: referrer._id,
    //                 fullName: referrer.name,
    //                 firstName,
    //                 lastName,
    //                 isBusiness: false,
    //                 companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
    //                 type: ContactTypeEnum.OTHER,
    //                 dateReceived: referrer.createdAt,
    //                 createdAt: referrer.createdAt,
    //                 updatedAt: referrer.updatedAt,
    //             };
    //         });

    //         await this.contactModel.insertMany(contactsToCreate);

    //         console.log("Successfully migrated referrer to contact");
    //     } catch (error) {
    //         console.error("Referrer migration failed:", error);
    //         throw new InternalServerErrorException("Failed to migrate referrer to contact");
    //     }
    // }

    // //migrate script to move opportunity activity into activity log
    // async migrateOpportunityActivity() {
    //     try {
    //         console.log("Starting opportunity activity migration");

    //         // Find all opportunities with clientId but no contactId
    //         const allLeads = await this.leadModel.find({ oppId: { $exists: false } }, { _id: 1 });

    //         console.log(allLeads.length);
    //         const leadctivity = await this.opportunityActivityModel.find({
    //             oppId: { $in: allLeads.map((lead) => lead._id) },
    //         });

    //         const createPromises2 = leadctivity.map((lead) => {
    //             return this.activityLogModel.create({
    //                 companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
    //                 moduleId: lead.oppId,
    //                 moduleType: "contact",
    //                 activities: lead.activities,
    //             });
    //         });

    //         await Promise.all(createPromises2);

    //         console.log("Successfully migrated lead activity to activity log");

    //         const opportunities = await this.opportunityActivityModel.find({
    //             oppId: { $nin: allLeads.map((lead) => lead._id) },
    //         });
    //         console.log(opportunities.length);

    //         // create activity log for each opportunity
    //         const createPromises = opportunities.map((opportunity) => {
    //             return this.activityLogModel.create({
    //                 companyId: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
    //                 moduleId: opportunity.oppId,
    //                 moduleType: "opportunity",
    //                 activities: opportunity.activities,
    //             });
    //         });

    //         await Promise.all(createPromises);

    //         console.log(
    //             `Successfully migrated ${opportunities.length} opportunities from opportunity activity to activity log`,
    //         );
    //     } catch (error) {
    //         console.error("Opportunity activity migration failed:", error);
    //         throw new InternalServerErrorException("Failed to migrate opportunity activity to activity log");
    //     }
    // }

    // move trackingAttribution into tracking
    async migrateTrackingAttribution() {
        try {
            console.log("Starting trackingAttribution migration");

            // Find all contacts with trackingAttribution
            const contacts = await this.contactModel.find({ trackingAttribution: { $exists: true } });
            const contacts2 = await this.contactModel.find({ attributes: { $exists: true } });

            // create activity log for each opportunity
            const updatePromises = contacts.map((contact: any) => {
                return this.contactModel.updateOne(
                    { _id: contact._id },
                    {
                        $push: { tracking: contact.trackingAttribution },
                        $unset: { trackingAttribution: 1 },
                    },
                );
            });
            // attributes
            const updatePromises2 = contacts2.map((contact: any) => {
                return this.contactModel.updateOne(
                    { _id: contact._id },
                    {
                        $set: { tracking: contact.attributes },
                        $unset: { attributes: 1 },
                    },
                );
            });

            await Promise.all(updatePromises);
            await Promise.all(updatePromises2);

            console.log("Successfully migrated trackingAttribution to tracking");
        } catch (error) {
            console.error("Tracking migration failed:", error);
            throw new InternalServerErrorException("Failed to migrate trackingAttribution to tracking");
        }
    }

    // script to migrate all contact that are isBussiness true to change firstName & lastName if firstName is split by space and index 0 will be first name index 1 will be last name
    async migrateBusinessName() {
        try {
            console.log("Starting business name migration");

            // Find all contacts with isBusiness true
            const contacts = await this.contactModel.find({ isBusiness: true });

            // create activity log for each opportunity
            const updatePromises = contacts.map((contact: any) => {
                if (contact.firstName.trim().includes(" ")) {
                    const names = contact.firstName.trim().split(" ");
                    const firstName = names[0];
                    const lastName = names.slice(1).join(" ");
                    return this.contactModel.updateOne(
                        { _id: contact._id },
                        {
                            $set: { firstName, lastName },
                        },
                    );
                }
            });
            console.log(updatePromises.length, "updatePromises");
            await Promise.all(updatePromises);

            // Find all contacts with isBusiness false and lastName is empty
            console.log("Starting business name migration 2");
            const contacts2 = await this.contactModel.find({
                isBusiness: false,
                $or: [{ lastName: { $exists: false } }, { lastName: "" }],
            });
            const updatePromises2 = contacts2.map((contact: any) => {
                if (contact.fullName.trim().includes(" ")) {
                    const names = contact.fullName.trim().split(" ");
                    const firstName = names[0];
                    const lastName = names.slice(1).join(" ");
                    return this.contactModel.updateOne(
                        { _id: contact._id },
                        {
                            $set: { firstName, lastName },
                        },
                    );
                }
            });
            console.log(updatePromises2.length, "updatePromises2");

            await Promise.all(updatePromises2);

            console.log("Successfully migrated business name");
        } catch (error) {
            console.error("Business name migration failed:", error);
            throw new InternalServerErrorException("Failed to migrate business name");
        }
    }

    // script to find all leads where contactId is not present then add contactId from contact by matching _id of contact and _id of lead
    async migrateLeadToContact2() {
        try {
            console.log("Starting lead to contact migration");

            // await this.leadModel.updateMany({ stage: { $exists: true } }, { $rename: { stage: "stageId" } });

            console.log("Successfully migrated lead stage");

            // Find all leads with no contactId
            const leads = await this.leadModel.find({ contactId: { $exists: false } });
            const contacts = await this.contactModel.find({
                _id: { $in: leads.map((lead) => lead._id) },
                stageId: { $exists: true },
            });
            console.log(leads.length, contacts.length);

            const updatePromises = contacts.map(async (contact: any) => {
                const opp = await this.opportunityModel.findOne({ contactId: contact._id }, { _id: 1 });
                // if (opp.length > 0) console.log(opp.length, contact._id);
                // return opp.length;
                await this.leadModel.updateOne(
                    { _id: contact._id, contactId: { $exists: false } },
                    {
                        $set: {
                            contactId: contact._id,
                            status: opp?._id && contact.status === "active" ? "converted" : contact.status,
                            deleted: contact.deleted,
                            csrId: contact.csrId,
                            ...(contact?.stageId ? { stageId: contact.stageId } : {}),
                            ...(opp?._id ? { oppId: opp._id } : {}),
                            ...(contact?.newLeadDate ? { newLeadDate: contact.newLeadDate } : {}),
                        },
                    },
                );
            });
            console.log(updatePromises.length, "updatePromises");

            const leads3 = await this.leadModel.find({
                contactId: { $exists: false },
                phone: { $exists: true },
            });
            const contacts3 = await this.contactModel.find({
                phone: { $in: leads3.map((lead) => lead.phone) },
                stageId: { $exists: true },
            });
            console.log(leads3.length, contacts3.length);
            const updatePromises3 = contacts3.map(async (contact: any) => {
                const opp = await this.opportunityModel.findOne({ contactId: contact._id }, { _id: 1 });
                // if (opp.length > 0) console.log(opp.length, contact._id);
                // return opp.length;
                await this.leadModel.updateOne(
                    { phone: contact.phone, contactId: { $exists: false } },
                    {
                        $set: {
                            contactId: contact._id,
                            status: opp?._id && contact.status === "active" ? "converted" : contact.status,
                            deleted: contact.deleted,
                            csrId: contact.csrId,
                            ...(contact?.stageId ? { stageId: contact.stageId } : {}),
                            ...(opp?._id ? { oppId: opp._id } : {}),
                            ...(contact?.newLeadDate ? { newLeadDate: contact.newLeadDate } : {}),
                        },
                    },
                );
            });
            console.log(updatePromises3.length, "updatePromises3");

            const leads2 = await this.leadModel.find({
                contactId: { $exists: false },
                email: { $exists: true },
            });
            const contacts2 = await this.contactModel.find({
                email: { $in: leads2.map((lead) => lead.email) },
                stageId: { $exists: true },
            });
            console.log(leads2.length, contacts2.length);

            // create activity log for each opportunity
            const updatePromises2 = contacts2.map(async (contact: any) => {
                const opp = await this.opportunityModel.findOne({ contactId: contact._id }, { _id: 1 });
                // if (opp.length > 0) console.log(opp.length, contact._id);
                // return opp.length;
                await this.leadModel.updateOne(
                    { email: contact.email, contactId: { $exists: false } },
                    {
                        $set: {
                            contactId: contact._id,
                            status: opp?._id && contact.status === "active" ? "converted" : contact.status,
                            deleted: contact.deleted,
                            csrId: contact.csrId,
                            ...(contact?.stageId ? { stageId: contact.stageId } : {}),
                            ...(opp?._id ? { oppId: opp._id } : {}),
                            ...(contact?.newLeadDate ? { newLeadDate: contact.newLeadDate } : {}),
                        },
                    },
                );
            });
            console.log(updatePromises2.length, "updatePromises2");

            // const leads4 = await this.leadModel.find({
            //     contactId: { $exists: false },
            //     phone: { $exists: true },
            // });
            // const contacts4 = await this.contactModel.find({
            //     phone: { $in: leads4.map((lead) => lead.phone) },
            // });
            // // remove duplicates from contacts4
            // const uniqueContacts4 = contacts4.filter(
            //     (contact, index, self) => index === self.findIndex((c) => c.phone === contact.phone),
            // );
            // console.log(leads4.length, contacts4.length, uniqueContacts4.length);
            // const updatePromises4 = uniqueContacts4.map((contact: any) => {
            //     if (contact.stageId) {
            //         return this.leadModel.updateOne(
            //             { phone: contact.phone, contactId: { $exists: false } },
            //             {
            //                 $set: { contactId: contact._id },
            //             },
            //         );
            //     }
            // });

            // await Promise.all(updatePromises4);

            // const leads5 = await this.leadModel.find({
            //     contactId: { $exists: false },
            //     email: { $exists: true },
            // });
            // const contacts5 = await this.contactModel.find({
            //     email: { $in: leads5.map((lead) => lead.email) },
            // });
            // console.log(leads5.length, contacts5.length);
            // const updatePromises5 = contacts5.map((contact: any) => {
            //     return this.leadModel.updateOne(
            //         { email: contact.email, contactId: { $exists: false } },
            //         {
            //             $set: { contactId: contact._id },
            //         },
            //     );
            // });

            // await Promise.all(updatePromises5);

            console.log("Successfully migrated lead to contact");
        } catch (error) {
            console.error("Lead to contact migration failed:", error);
            throw new InternalServerErrorException("Failed to migrate lead to contact");
        }
    }

    // check all contact which have newLeadDate and have stageId then check if it has a lead for that contact
    async checkLeadForContact() {
        try {
            console.log("Starting lead check for contact");

            const contacts = await this.contactModel.find({
                newLeadDate: { $exists: true },
                stageId: { $exists: true },
            });
            console.log(contacts.length);

            // check if contact has a lead
            const updatePromises = contacts.map(async (contact: any) => {
                const lead = await this.leadModel.findOne({ contactId: contact._id });
                if (!lead) {
                    console.log("No lead found for contact", contact._id);
                    // create lead
                    const l = await this.leadModel.create({
                        contactId: contact._id,
                        stageId: contact.stageId,
                        status: contact.status,
                        deleted: contact.deleted,
                        csrId: contact.csrId,
                        newLeadDate: contact.newLeadDate,
                        tracking: contact?.tracking[0] || {},
                        createdBy: contact.createdBy,
                        createdAt: contact.createdAt,
                        zapierLead: contact.zapierLead,
                        workType: contact.workType,
                        referredBy: contact?.referredBy,
                    });
                    console.log("Created lead for contact", l._id);
                }
            });

            console.log("Successfully migrated lead stage");
        } catch (error) {
            console.error("Lead stage migration failed:", error);
            throw new InternalServerErrorException("Failed to migrate lead stage");
        }
    }

    // script for duplicate contacts
    // async migrateDuplicateContacts() {
    //     try {
    //         console.log("Starting duplicate contact migration");

    //         const duplicatePhones = await this.contactModel.aggregate([
    //             {
    //                 $group: {
    //                     _id: "$phone",
    //                     count: { $sum: 1 },
    //                     docs: {
    //                         $push: {
    //                             _id: "$_id",
    //                             fullName: "$fullName",
    //                             phone: "$phone",
    //                             email: "$email",
    //                             stageId: "$stageId",
    //                         },
    //                     },
    //                 },
    //             },
    //             {
    //                 $match: {
    //                     count: { $gt: 1 },
    //                 },
    //             },
    //             {
    //                 $unwind: "$docs",
    //             },
    //             {
    //                 $replaceRoot: {
    //                     newRoot: "$docs",
    //                 },
    //             },
    //             {
    //                 $match: {
    //                     stageId: { $exists: true },
    //                     phone: { $exists: true },
    //                 },
    //             },
    //             {
    //                 $project: {
    //                     _id: 1,
    //                     fullName: 1,
    //                     phone: 1,
    //                     email: 1,
    //                     stageId: 1,
    //                 },
    //             },
    //         ]);
    //         console.log(`Found ${duplicatePhones.length} phone numbers with duplicates`);

    //         let totalUpdated = 0;

    //         // Process each group of duplicates
    //         for (const phoneGroup of duplicatePhones) {
    //             // Sort by updatedAt descending to get the most recent first
    //             const sortedDocs = phoneGroup.docs.sort((a, b) => b.updatedAt - a.updatedAt);

    //             // Keep the first one (most recent), mark others as deleted
    //             const idsToDelete = sortedDocs.slice(1).map((doc) => doc._id);

    //             if (idsToDelete.length > 0) {
    //                 const result = await this.contactModel.updateMany(
    //                     { _id: { $in: idsToDelete } },
    //                     {
    //                         $set: {
    //                             deleted: true,
    //                             deletedAt: new Date(),
    //                             deletedReason: "Duplicate phone number - older record",
    //                         },
    //                     },
    //                 );

    //                 totalUpdated += result.modifiedCount;
    //                 console.log(
    //                     `Phone: ${phoneGroup._id} - Marked ${result.modifiedCount} older records as deleted`,
    //                 );
    //             }
    //         }

    //         console.log(`Total records marked as deleted: ${totalUpdated}`);
    //     } catch (error) {
    //         console.error("Duplicate contact migration failed:", error);
    //         throw new InternalServerErrorException("Failed to migrate duplicate contacts");
    //     }
    // }

    // script to migrate stage name to stageId on lead schema
    async migrateLeadStage() {
        try {
            console.log("Starting lead stage migration");

            await this.leadModel.updateMany({ stage: { $exists: true } }, { $rename: { stage: "stageId" } });

            console.log("Successfully migrated lead stage");

            // find all leads where stageId is not present then add stageId from contact by matching _id of contact and _id of lead
            // const contacts = await this.contactModel.find({
            //     stageId: { $exists: true },
            // });
            // console.log(contacts.length);
            // const updatePromises = contacts.map((contact: any) => {
            //     return this.leadModel.updateOne(
            //         { contactId: contact._id },
            //         {
            //             $set: { stageId: contact.stageId, status: contact.status },
            //         },
            //     );
            // });
            // await Promise.all(updatePromises);

            // await this.leadModel.updateMany({ lost: true }, { $set: { status: "lost" } });

            // const leads = await this.leadModel.find({});
            // const contacts2 = await this.contactModel.find({
            //     _id: { $in: leads.map((lead) => lead.contactId) },
            //     stageId: { $exists: true },
            // });
            // console.log(leads.length, contacts2.length);
            // const updatePromises2 = contacts2.map((contact: any) => {
            //     return this.leadModel.updateOne(
            //         { contactId: contact._id },
            //         {
            //             $set: { stageId: contact.stageId },
            //         },
            //     );
            // });

            // await Promise.all(updatePromises2);

            // const contacts3 = await this.contactModel.find({
            //     _id: { $in: leads.map((lead) => lead.contactId) },
            // });
            // console.log(leads.length, contacts3.length);
            // const updatePromises3 = contacts3.map((contact: any) => {
            //     return this.leadModel.updateOne(
            //         { contactId: contact._id },
            //         {
            //             $set: { status: contact.status },
            //         },
            //     );
            // });
            // await Promise.all(updatePromises3);

            // only lead type contact
            // const contacts4 = await this.contactModel.find({
            //     type: ContactTypeEnum.LEAD,
            // });
            // const leads4 = await this.leadModel.find({
            //     contactId: { $in: contacts4.map((con) => con._id) },
            // });
            // console.log(contacts4.length, leads4.length, "Lead Type");
            // const updatePromises4 = contacts4.map((contact: any) => {
            //     return this.leadModel.updateOne(
            //         { contactId: contact._id },
            //         {
            //             $set: { status: contact.status, stageId: contact.stageId, deleted: contact.deleted },
            //         },
            //     );
            // });
            // console.log(updatePromises4.length, "updatePromises4");
            // await Promise.all(updatePromises4);

            console.log("Successfully migrated lead stage");
        } catch (error) {
            console.error("Lead stage migration failed:", error);
            throw new InternalServerErrorException("Failed to migrate lead stage");
        }
    }

    // Script to fix incorrect contactId assignments after migrateLeadToContact2
    async fixIncorrectContactIdAssignments() {
        try {
            console.log("Starting fix for incorrect contactId assignments");

            let fixedCount = 0;
            let noPhoneNoEmailCount = 0;
            let noMatchFoundCount = 0;
            let alreadyCorrectCount = 0;
            let wrongContactPhone = 0;
            let wrongContactEmail = 0;
            let oppCount = 0;
            const noMatchFound = [];
            const noPhoneNoEmail = [];

            // Get all leads where _id is not matching with contactId (indicating potential wrong assignment)
            const leadsWithMismatchedContactId = await this.leadModel
                .find({
                    contactId: { $exists: true },
                    // $expr: { $ne: ["$_id", "$contactId"] },
                })
                .select("_id contactId phone email companyId firstName lastName");

            console.log(`Found ${leadsWithMismatchedContactId.length} leads with mismatched contactId`);

            for (const leadData of leadsWithMismatchedContactId) {
                const lead: any = leadData;
                // console.log(`\n--- Processing Lead ID: ${lead._id} ---`);
                // console.log(`Current contactId: ${lead.contactId}`);
                // console.log(`Lead phone: ${lead.phone || "N/A"}`);
                // console.log(`Lead email: ${lead.email || "N/A"}`);

                let correctContact = null;
                let matchType = "";

                // First try to match by phone if lead has phone
                if (lead.phone) {
                    correctContact = await this.contactModel
                        .findOne({
                            phone: lead.phone,
                            companyId: lead.companyId,
                            // deleted: { $ne: true },
                        })
                        .select("_id phone email fullName");

                    if (correctContact) {
                        matchType = "phone";
                        console.log(
                            `Found contact by phone: ${correctContact._id} (${correctContact.fullName})`,
                        );
                    }
                }

                // If no phone match and lead has email, try to match by email
                if (!correctContact && lead.email) {
                    correctContact = await this.contactModel
                        .findOne({
                            email: lead.email,
                            companyId: lead.companyId,
                            // deleted: { $ne: true },
                        })
                        .select("_id phone email fullName");

                    if (correctContact) {
                        matchType = "email";
                        console.log(
                            `Found contact by email: ${correctContact._id} (${correctContact.fullName})`,
                        );
                    }
                }

                // Check if the current contactId is already correct
                if (correctContact && correctContact._id === lead.contactId) {
                    // console.log(`✓ ContactId is already correct for lead ${lead._id}`);
                    alreadyCorrectCount++;
                    continue;
                }

                // If we found a correct contact and it's different from current contactId
                if (correctContact && correctContact._id !== lead.contactId) {
                    if (matchType === "phone") {
                        wrongContactPhone++;
                    } else if (matchType === "email") {
                        wrongContactEmail++;
                    }
                    // console.log(
                    //     `🔄 Need to update contactId from ${lead.contactId} to ${correctContact._id} (matched by ${matchType})`,
                    // );

                    // Get the opportunity for this correct contact
                    const opps = await this.opportunityModel.find(
                        {
                            contactId: correctContact._id,
                        },
                        { _id: 1, newLeadDate: 1, street: 1 },
                    );
                    if (opps.length > 0) {
                        oppCount++;
                    }

                    const opp = opps.length > 1 ? opps.find((opp) => opp.street === lead.street) : opps[0];

                    // Get the correct contact's full data for other fields
                    const fullCorrectContact = await this.contactModel.findById(correctContact._id);

                    // Update the lead with correct contactId and related fields
                    const updateData = {
                        contactId: correctContact._id,
                        ...(fullCorrectContact?.status && {
                            status:
                                opp?._id && fullCorrectContact.status === "active"
                                    ? "converted"
                                    : fullCorrectContact.status,
                        }),
                        ...(fullCorrectContact?.deleted !== undefined && {
                            deleted: fullCorrectContact.deleted,
                        }),
                        ...(fullCorrectContact?.csrId && {
                            csrId: fullCorrectContact.csrId,
                        }),
                        ...(fullCorrectContact?.stageId && { stageId: fullCorrectContact.stageId }),
                        ...(opp?._id && { oppId: opp._id }),
                        ...(fullCorrectContact?.newLeadDate && {
                            newLeadDate: fullCorrectContact.newLeadDate,
                        }),
                    };

                    // console.log(`Update data:`, updateData);

                    // COMMENTED OUT FOR REVIEW - UNCOMMENT TO ACTUALLY UPDATE
                    // /*
                    await this.leadModel.updateOne({ _id: lead._id }, { $set: updateData });
                    // */

                    console.log(
                        `✅ Would update lead ${lead._id} with correct contactId ${correctContact._id} opp length ${opps.length}`,
                    );
                    fixedCount++;
                } else if (!correctContact) {
                    if (!lead.phone && !lead.email) {
                        console.log(`❌ Lead ${lead._id} has no phone or email to match with`);
                        noPhoneNoEmailCount++;
                        noPhoneNoEmail.push(lead._id);
                    } else {
                        console.log(`❌ No matching contact found for lead ${lead._id}`);
                        noMatchFoundCount++;
                        noMatchFound.push(lead._id);
                    }
                }
            }

            console.log("\n=== SUMMARY ===");
            console.log(`Total leads processed: ${leadsWithMismatchedContactId.length}`);
            console.log(`Wrong leads with phone number: ${wrongContactPhone}`);
            console.log(`Wrong leads with email: ${wrongContactEmail}`);
            console.log(`Leads that would be fixed: ${fixedCount}`);
            console.log(`Leads already correct: ${alreadyCorrectCount}`);
            console.log(`Leads with no phone/email: ${noPhoneNoEmailCount}`);
            console.log(`Leads with no matching contact: ${noMatchFoundCount}`);
            console.log(`Leads with opportunity: ${oppCount}`);
            console.log(noPhoneNoEmail);
            console.log(noMatchFound);
            // console.log("\n⚠️  THIS WAS A DRY RUN - NO ACTUAL UPDATES WERE MADE");
            // console.log("⚠️  UNCOMMENT THE UPDATE QUERY IN THE CODE TO APPLY CHANGES");
        } catch (error) {
            console.error("Fix incorrect contactId assignments failed:", error);
            throw new InternalServerErrorException("Failed to fix incorrect contactId assignments");
        }
    }

    // write code to find all leads where newLeadDate is not in 12hr to 24hr range of createdAt
    async fixNewLeadDate() {
        try {
            const leads: any = await this.leadModel
                .find({
                    newLeadDate: { $exists: true },
                })
                .populate("contactId", "newLeadDate dateReceived", "Contact");
            console.log("Starting fix for newLeadDate");

            let count = 0;
            for (const lead of leads) {
                const newLeadDate = new Date(lead.newLeadDate);
                const createdAt = new Date(lead.createdAt);
                const dateReceived = new Date(lead?.contactId?.dateReceived);
                const newLeadDate2 = new Date(lead?.contactId?.newLeadDate);

                const timeDifference = Math.abs(newLeadDate.getTime() - createdAt.getTime());
                const timeDifference2 = Math.abs(newLeadDate.getTime() - newLeadDate2.getTime());
                const timeDifference3 = Math.abs(newLeadDate.getTime() - dateReceived.getTime());
                const hoursDifference = timeDifference / (1000 * 60 * 60);
                const hoursDifference2 = timeDifference2 / (1000 * 60 * 60);
                const hoursDifference3 = timeDifference3 / (1000 * 60 * 60);

                if (hoursDifference > 24) {
                    count++;
                    // console.log(`${count} Lead ${lead?._id}  - ${hoursDifference} hours difference`);

                    // we need to find the correct newLeadDate
                    if (hoursDifference2 < 24 && hoursDifference2 > 0) {
                        console.log(
                            `${count} Lead ${lead?._id}  - ${hoursDifference} hours difference - ${hoursDifference2} hours difference with contact`,
                        );
                    }
                    if (hoursDifference3 < 24 && hoursDifference3 > 0) {
                        console.log(
                            `${count} Lead ${lead?._id}  - ${hoursDifference} hours difference - ${hoursDifference3} hours difference with dateReceived`,
                        );
                    }

                    if (lead?.oppId) {
                        const opp = await this.opportunityModel.findOne(
                            { _id: lead?.oppId },
                            { contactId: 1, newLeadDate: 1 },
                        );
                        if (opp) {
                            const newLeadDate3 = new Date(opp.newLeadDate);
                            const timeDifference4 = Math.abs(newLeadDate.getTime() - newLeadDate3.getTime());
                            const hoursDifference4 = timeDifference4 / (1000 * 60 * 60);
                            if (hoursDifference4 > 24) {
                                console.log(
                                    `${count} Lead ${lead?._id}  - ${hoursDifference} hours difference - ${hoursDifference4} hours difference with opportunity`,
                                );
                            }
                        }
                    }
                }
            }

            console.log(`Found ${leads.length} leads with newLeadDate`);
        } catch (error) {
            console.error("Fix newLeadDate failed:", error);
            throw new InternalServerErrorException("Failed to fix newLeadDate");
        }
    }

    /**
     * Migration function to update contact types based on their opportunities
     *
     * Logic:
     * - If contact has opportunity but no saleDate or orderId → type: "prospect"
     * - If contact has opportunity with both saleDate and orderId → type: "client"
     * - If contact has no opportunity → don't update type
     *
     * @param dryRun - If true, only logs what would be changed without making updates
     */
    async migrateContactTypesByOpportunity(dryRun = true) {
        try {
            console.log("Starting contact type migration based on opportunities");
            console.log(`DRY RUN MODE: ${dryRun ? "ON" : "OFF"}`);

            let prospectCount = 0;
            let clientCount = 0;
            let noOpportunityCount = 0;
            let alreadyCorrectCount = 0;
            let errorCount = 0;

            // Get all all contacts & opportunities
            const [contacts, opps] = await Promise.all([
                this.contactModel
                    .find({
                        // deleted: { $ne: true },
                    })
                    .select("_id fullName firstName lastName type companyId"),
                this.opportunityModel
                    .find({
                        // deleted: { $ne: true },
                    })
                    .select("_id contactId saleDate orderId"),
            ]);

            console.log(`Found ${contacts.length} contacts to process`);

            for (const contact of contacts) {
                try {
                    console.log(
                        `\n--- Processing Contact: ${contact.fullName || contact.firstName} (${
                            contact._id
                        }) ---`,
                    );
                    console.log(`Current type: ${contact.type}`);

                    // Find opportunities for this contact
                    const opportunities = opps.filter(
                        (opp) => opp.contactId.toString() === contact._id.toString(),
                    );

                    console.log(`Found ${opportunities.length} opportunities for this contact`);

                    if (opportunities.length === 0) {
                        console.log(`❌ No opportunities found - skipping type update`);
                        noOpportunityCount++;
                        continue;
                    }

                    // Determine the new type based on opportunities
                    let newType = null;
                    let hasClientOpportunity = false;

                    for (const opp of opportunities) {
                        console.log(
                            `  Opportunity ${opp._id}: saleDate=${opp.saleDate ? "YES" : "NO"}, orderId=${
                                opp.orderId ? "YES" : "NO"
                            }`,
                        );

                        // If any opportunity has both saleDate and orderId, contact should be client
                        if (opp.saleDate && opp.orderId) {
                            hasClientOpportunity = true;
                            break;
                        }
                    }

                    if (hasClientOpportunity) {
                        newType = ContactTypeEnum.CLIENT;
                        console.log(`🔄 Should be CLIENT (has opportunity with saleDate and orderId)`);
                    } else {
                        newType = ContactTypeEnum.PROSPECT;
                        console.log(`🔄 Should be PROSPECT (has opportunity but no saleDate/orderId)`);
                    }

                    // Check if type is already correct
                    if (contact.type === newType) {
                        console.log(`✓ Type is already correct (${newType})`);
                        alreadyCorrectCount++;
                        continue;
                    }

                    console.log(`🔄 Need to update type from "${contact.type}" to "${newType}"`);

                    if (!dryRun) {
                        await this.contactModel.updateOne({ _id: contact._id }, { $set: { type: newType } });
                        console.log(`✅ Updated contact ${contact._id} type to ${newType}`);
                    } else {
                        console.log(`✅ Would update contact ${contact._id} type to ${newType}`);
                    }

                    if (newType === ContactTypeEnum.PROSPECT) {
                        prospectCount++;
                    } else if (newType === ContactTypeEnum.CLIENT) {
                        clientCount++;
                    }
                } catch (contactError) {
                    console.error(`❌ Error processing contact ${contact._id}:`, contactError.message);
                    errorCount++;
                }
            }

            console.log("\n=== MIGRATION SUMMARY ===");
            console.log(`Total contacts processed: ${contacts.length}`);
            console.log(`Contacts ${dryRun ? "that would be" : ""} updated to PROSPECT: ${prospectCount}`);
            console.log(`Contacts ${dryRun ? "that would be" : ""} updated to CLIENT: ${clientCount}`);
            console.log(`Contacts already correct: ${alreadyCorrectCount}`);
            console.log(`Contacts with no opportunities (skipped): ${noOpportunityCount}`);
            console.log(`Contacts with errors: ${errorCount}`);

            if (dryRun) {
                console.log("\n⚠️  THIS WAS A DRY RUN - NO ACTUAL UPDATES WERE MADE");
                console.log("⚠️  Call migrateContactTypesByOpportunity(false) TO APPLY CHANGES");
            } else {
                console.log("\n✅ MIGRATION COMPLETED - UPDATES HAVE BEEN APPLIED TO THE DATABASE");
            }

            return new OkResponse({
                message: "Contact type migration completed",
                summary: {
                    totalProcessed: contacts.length,
                    prospectUpdates: prospectCount,
                    clientUpdates: clientCount,
                    alreadyCorrect: alreadyCorrectCount,
                    noOpportunities: noOpportunityCount,
                    errors: errorCount,
                    dryRun,
                },
            });
        } catch (error) {
            console.error("Contact type migration failed:", error);
            throw new InternalServerErrorException("Failed to migrate contact types");
        }
    }
}
